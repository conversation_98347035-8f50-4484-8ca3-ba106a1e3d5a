// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get done => 'किया हुआ';

  @override
  String get loading => 'लोड हो रहा है ...';

  @override
  String get messageHasBeenDeleted => 'संदेश हटा दिया गया है';

  @override
  String get mute => 'म्यूट';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get typing => 'लिख रहा है...';

  @override
  String get ok => 'ठीक है';

  @override
  String get recording => 'रेकॉर्ड हो रहा है...';

  @override
  String get connecting => 'कनेक्ट हो रहा है...';

  @override
  String get deleteYouCopy => 'अपनी प्रतिलिपि मिटाएं';

  @override
  String get unMute => 'अनम्यूट';

  @override
  String get delete => 'हटाएं';

  @override
  String get report => 'रिपोर्ट';

  @override
  String get leaveGroup => 'समूह छोड़ें';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'क्या आप निश्चित हैं कि आप अपनी प्रतिलिपि की अनुमति देना चाहते हैं? यह कार्रवाई पूर्वरूप में नहीं ली जा सकती';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'क्या आप इस समूह को छोड़ने के लिए निश्चित हैं? यह कार्रवाई पूर्वरूप में नहीं ली जा सकती';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'समूह छोड़कर अपनी संदेश प्रतिलिपि को हटाएं';

  @override
  String get vMessageInfoTrans => 'संदेश जानकारी';

  @override
  String get updateTitleTo => 'शीर्षक अपडेट करें';

  @override
  String get updateImage => 'छवि अपडेट करें';

  @override
  String get joinedBy => 'के द्वारा जुड़े';

  @override
  String get promotedToAdminBy => 'के द्वारा प्रशासक बनाया गया';

  @override
  String get dismissedToMemberBy => 'के द्वारा सदस्य बनाया गया';

  @override
  String get leftTheGroup => 'समूह छोड़ दिया';

  @override
  String get you => 'आप';

  @override
  String get kickedBy => 'के द्वारा किया गया';

  @override
  String get groupCreatedBy => 'समूह द्वारा बनाया गया';

  @override
  String get addedYouToNewBroadcast => 'नए प्रसारण में आपको जोड़ दिया गया';

  @override
  String get download => 'डाउनलोड';

  @override
  String get copy => 'कॉपी';

  @override
  String get info => 'जानकारी';

  @override
  String get share => 'शेयर';

  @override
  String get forward => 'फ़ॉरवर्ड';

  @override
  String get reply => 'जवाब दें';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'सभी से हटाएं';

  @override
  String get deleteFromMe => 'मुझसे हटाएं';

  @override
  String get downloading => 'डाउनलोड हो रहा है...';

  @override
  String get fileHasBeenSavedTo => 'फ़ाइल को सहेज लिया गया है';

  @override
  String get online => 'ऑनलाइन';

  @override
  String get youDontHaveAccess => 'आपके पास पहुँचने का अधिकार नहीं है';

  @override
  String get replyToYourSelf => 'अपने आप को जवाब दें';

  @override
  String get repliedToYourSelf => 'अपने आप को जवाब दिया';

  @override
  String get audioCall => 'ऑडियो कॉल';

  @override
  String get ring => 'रिंग';

  @override
  String get canceled => 'रद्द किया गया';

  @override
  String get timeout => 'समय समाप्ति';

  @override
  String get rejected => 'अस्वीकृत';

  @override
  String get finished => 'समाप्त';

  @override
  String get inCall => 'कॉल में';

  @override
  String get sessionEnd => 'सत्र समाप्ति';

  @override
  String get yesterday => 'कल';

  @override
  String get today => 'आज';

  @override
  String get textFieldHint => 'संदेश टाइप करें...';

  @override
  String get files => 'फ़ाइलें';

  @override
  String get location => 'स्थान';

  @override
  String get shareMediaAndLocation => 'मीडिया और स्थान साझा करें';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'वीडियो का आकार अनुमत आकार से बड़ा है';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'फ़ाइल का आकार अनुमत आकार से बड़ा है';

  @override
  String get makeCall => 'कॉल करें';

  @override
  String get areYouWantToMakeVideoCall => 'क्या आप वीडियो कॉल करना चाहते हैं?';

  @override
  String get areYouWantToMakeVoiceCall => 'क्या आप वॉयस कॉल करना चाहते हैं?';

  @override
  String get vMessagesInfoTrans => 'संदेश जानकारी';

  @override
  String get star => 'स्टार';

  @override
  String get minutes => 'मिनट';

  @override
  String get sendMessage => 'संदेश भेजें';

  @override
  String get deleteUser => 'उपयोगकर्ता हटाएं';

  @override
  String get actions => 'क्रियाएँ';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'आप अपनी सूची से इस उपयोगकर्ता को हटाने के बारे में हैं';

  @override
  String get updateBroadcastTitle => 'प्रसारण शीर्षक अपडेट करें';

  @override
  String get usersAddedSuccessfully => 'उपयोगकर्ता सफलतापूर्वक जोड़े गए हैं';

  @override
  String get broadcastSettings => 'प्रसारण सेटिंग्स';

  @override
  String get addParticipants => 'सहभागियों को जोड़ें';

  @override
  String get broadcastParticipants => 'प्रसारण सहभागियों';

  @override
  String get updateGroupDescription => 'समूह विवरण अपडेट करें';

  @override
  String get updateGroupTitle => 'समूह शीर्षक अपडेट करें';

  @override
  String get groupSettings => 'समूह सेटिंग्स';

  @override
  String get description => 'विवरण';

  @override
  String get muteNotifications => 'सूचनाओं को म्यूट करें';

  @override
  String get groupParticipants => 'समूह सहभागी';

  @override
  String get blockUser => 'उपयोगकर्ता को ब्लॉक करें';

  @override
  String get areYouSureToBlock =>
      'क्या आप निश्चित हैं कि आप ब्लॉक करना चाहते हैं';

  @override
  String get userPage => 'उपयोगकर्ता पेज';

  @override
  String get starMessage => 'संदेश को स्टार करें';

  @override
  String get showMedia => 'मीडिया दिखाएं';

  @override
  String get reportUser => 'उपयोगकर्ता की रिपोर्ट करें';

  @override
  String get groupName => 'समूह नाम';

  @override
  String get changeSubject => 'विषय बदलें';

  @override
  String get titleIsRequired => 'शीर्षक आवश्यक है';

  @override
  String get createBroadcast => 'प्रसारण बनाएं';

  @override
  String get broadcastName => 'प्रसारण नाम';

  @override
  String get createGroup => 'समूह बनाएं';

  @override
  String get forgetPassword => 'पासवर्ड भूल गए';

  @override
  String get globalSearch => 'वैश्विक खोज';

  @override
  String get dismissesToMember => 'सदस्य को निष्कासित करें';

  @override
  String get setToAdmin => 'प्रशासक के रूप में सेट करें';

  @override
  String get kickMember => 'सदस्य को निकालें';

  @override
  String get youAreAboutToDismissesToMember =>
      'आप सदस्य को निष्कासित करने के बारे में हैं';

  @override
  String get youAreAboutToKick => 'आप किक करने के बारे में हैं';

  @override
  String get groupMembers => 'समूह सदस्य';

  @override
  String get tapForPhoto => 'फ़ोटो के लिए टैप करें';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'हम इस अपडेट को डाउनलोड करने की उच्च सिफारिश करते हैं';

  @override
  String get newGroup => 'नया समूह';

  @override
  String get newBroadcast => 'नई प्रसारण';

  @override
  String get starredMessage => 'स्टार दिया गया संदेश';

  @override
  String get settings => 'सेटिंग्स';

  @override
  String get chats => 'चैट्स';

  @override
  String get recentUpdates => 'हाल की अपडेट्स';

  @override
  String get startChat => 'चैट शुरू करें';

  @override
  String get newUpdateIsAvailable => 'नई अपडेट उपलब्ध है';

  @override
  String get emailNotValid => 'ईमेल मान्य नहीं है';

  @override
  String get passwordMustHaveValue => 'पासवर्ड में मूल्य होना चाहिए';

  @override
  String get error => 'त्रुटि';

  @override
  String get password => 'पासवर्ड';

  @override
  String get login => 'लॉगिन';

  @override
  String get needNewAccount => 'नया खाता चाहिए?';

  @override
  String get register => 'रजिस्टर करें';

  @override
  String get nameMustHaveValue => 'नाम में मूल्य होना चाहिए';

  @override
  String get passwordNotMatch => 'पासवर्ड मेल नहीं खाता';

  @override
  String get name => 'नाम';

  @override
  String get email => 'ईमेल';

  @override
  String get confirmPassword => 'पासवर्ड की पुष्टि करें';

  @override
  String get alreadyHaveAnAccount => 'पहले से ही खाता है?';

  @override
  String get logOut => 'लॉग आउट';

  @override
  String get back => 'वापस';

  @override
  String get sendCodeToMyEmail => 'मेरे ईमेल पर कोड भेजें';

  @override
  String get invalidLoginData => 'अमान्य लॉगिन डेटा';

  @override
  String get userEmailNotFound => 'उपयोगकर्ता ईमेल नहीं मिला';

  @override
  String get yourAccountBlocked => 'आपका खाता ब्लॉक कर दिया गया है';

  @override
  String get yourAccountDeleted => 'आपका खाता हटा दिया गया है';

  @override
  String get userAlreadyRegister => 'उपयोगकर्ता पहले से ही पंजीकृत है';

  @override
  String get codeHasBeenExpired => 'कोड की समय सीमा समाप्त हो गई है';

  @override
  String get invalidCode => 'अमान्य कोड';

  @override
  String get whileAuthCanFindYou => 'प्रमाणीकरण के दौरान आपको नहीं मिल सकता';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'उपयोगकर्ता पंजीकरण स्थिति अभी तक स्वीकृत नहीं हुई है';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'उपकरण को सभी उपकरणों से लॉगआउट कर दिया गया है';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'उपयोगकर्ता डिवाइस सत्र समाप्त हुआ डिवाइस हटा दिया गया है';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'आपको अपने ईमेल की पुष्टि के लिए कोई कोड नहीं भेजा गया है';

  @override
  String get roomAlreadyInCall => 'कमरा पहले से ही कॉल में है';

  @override
  String get peerUserInCallNow => 'उपयोगकर्ता अब कॉल में है';

  @override
  String get callNotAllowed => 'कॉल अनुमत नहीं है';

  @override
  String get peerUserDeviceOffline => 'पीयर उपयोगकर्ता डिवाइस ऑफ़लाइन है';

  @override
  String get emailMustBeValid => 'ईमेल मान्य होनी चाहिए';

  @override
  String get wait2MinutesToSendMail => 'मेल भेजने के लिए 2 मिनट का इंतजार करें';

  @override
  String get codeMustEqualToSixNumbers => 'कोड छः अंकों के बराबर होना चाहिए';

  @override
  String get newPasswordMustHaveValue => 'नया पासवर्ड में मूल्य होना चाहिए';

  @override
  String get confirmPasswordMustHaveValue =>
      'पासवर्ड की पुष्टि में मूल्य होना चाहिए';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'समूह की ओर से आपका खाता स्वीकृत किया गया है';

  @override
  String get yourAccountIsUnderReview => 'आपका खाता समीक्षा के अंदर है';

  @override
  String get waitingList => 'प्रतीक्षा सूची';

  @override
  String get welcome => 'स्वागत है';

  @override
  String get retry => 'पुन: प्रयास करें';

  @override
  String get deleteMember => 'सदस्य हटाएं';

  @override
  String get profile => 'प्रोफ़ाइल';

  @override
  String get broadcastInfo => 'प्रसारण जानकारी';

  @override
  String get updateTitle => 'शीर्षक अपडेट करें';

  @override
  String get members => 'सदस्य';

  @override
  String get addMembers => 'सदस्य जोड़ें';

  @override
  String get success => 'सफलता';

  @override
  String get media => 'मीडिया';

  @override
  String get docs => 'दस्तावेज़';

  @override
  String get links => 'लिंक्स';

  @override
  String get soon => 'जल्दी ही';

  @override
  String get unStar => 'स्टार हटाएं';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'समूह विवरण अपडेट करने से सभी समूह सदस्य अपडेट हो जाएंगे';

  @override
  String get updateNickname => 'उपनाम अपडेट करें';

  @override
  String get groupInfo => 'समूह जानकारी';

  @override
  String get youNotParticipantInThisGroup =>
      'आप इस समूह में प्रतिभागी नहीं हैं';

  @override
  String get search => 'खोज';

  @override
  String get mediaLinksAndDocs => 'मीडिया, लिंक्स, और दस्तावेज़';

  @override
  String get starredMessages => 'स्टार दिए गए संदेश';

  @override
  String get nickname => 'उपनाम';

  @override
  String get none => 'कोई नहीं';

  @override
  String get yes => 'हाँ';

  @override
  String get no => 'नहीं';

  @override
  String get exitGroup => 'समूह से बाहर निकलें';

  @override
  String get clickToAddGroupDescription =>
      'समूह विवरण जोड़ने के लिए क्लिक करें';

  @override
  String get unBlockUser => 'उपयोगकर्ता को अनब्लॉक करें';

  @override
  String get areYouSureToUnBlock =>
      'क्या आप सुनिश्चित हैं कि आप अनब्लॉक करना चाहते हैं';

  @override
  String get contactInfo => 'संपर्क जानकारी';

  @override
  String get audio => 'ऑडियो';

  @override
  String get video => 'वीडियो';

  @override
  String get hiIamUse => 'हाय मैं उपयोग करता हूँ';

  @override
  String get on => 'चालू';

  @override
  String get off => 'बंद';

  @override
  String get unBlock => 'अनब्लॉक';

  @override
  String get block => 'ब्लॉक';

  @override
  String get chooseAtLestOneMember => 'कम से कम एक सदस्य चुनें';

  @override
  String get close => 'बंद करें';

  @override
  String get next => 'आगे';

  @override
  String get appMembers => 'ऐप सदस्य';

  @override
  String get create => 'बनाएं';

  @override
  String get upgradeToAdmin => 'व्यवस्थापक के रूप में अपग्रेड करें';

  @override
  String get update => 'अपडेट';

  @override
  String get deleteChat => 'चैट हटाएं';

  @override
  String get clearChat => 'चैट साफ करें';

  @override
  String get showHistory => 'इतिहास दिखाएं';

  @override
  String get groupIcon => 'समूह प्रतीक';

  @override
  String get tapToSelectAnIcon => 'एक प्रतीक का चयन करने के लिए टैप करें';

  @override
  String get groupDescription => 'समूह विवरण';

  @override
  String get more => 'अधिक';

  @override
  String get messageInfo => 'संदेश जानकारी';

  @override
  String get successfullyDownloadedIn => 'सफलतापूर्वक डाउनलोड किया गया है';

  @override
  String get delivered => 'पहुँचाया गया';

  @override
  String get read => 'पढ़ा गया';

  @override
  String get orLoginWith => 'या लॉगिन करें';

  @override
  String get resetPassword => 'पासवर्ड रीसेट करें';

  @override
  String get otpCode => 'OTP कोड';

  @override
  String get newPassword => 'नया पासवर्ड';

  @override
  String get areYouSure => 'क्या आपको यकीन है?';

  @override
  String get broadcastMembers => 'प्रसारण सदस्य';

  @override
  String get phone => 'फ़ोन';

  @override
  String get users => 'उपयोगकर्ता';

  @override
  String get calls => 'कॉल्स';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'आप इस खाते से लॉगआउट करने के बारे में हैं';

  @override
  String get noUpdatesAvailableNow => 'अब कोई अपडेट उपलब्ध नहीं है';

  @override
  String get dataPrivacy => 'डेटा गोपनीयता';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'सभी डेटा का बैकअप कर लिया गया है, आपको स्वयं डेटा को प्रबंधित करने की आवश्यकता नहीं है! यदि आप लॉगआउट करते हैं और फिर से लॉगिन करते हैं, तो आपको सभी चैट्स दिखाई देंगे, वेब संस्करण के लिए भी समान';

  @override
  String get account => 'खाता';

  @override
  String get linkedDevices => 'लिंक की गई डिवाइसेस';

  @override
  String get storageAndData => 'स्टोरेज और डेटा';

  @override
  String get tellAFriend => 'दोस्त को बताएं';

  @override
  String get help => 'सहायता';

  @override
  String get blockedUsers => 'ब्लॉक किए गए उपयोगकर्ता';

  @override
  String get inAppAlerts => 'ऐप अलर्ट्स';

  @override
  String get language => 'भाषा';

  @override
  String get adminNotification => 'एडमिन अधिसूचना';

  @override
  String get checkForUpdates => 'अपडेट की जाँच करें';

  @override
  String get linkByQrCode => 'क्यूआर कोड द्वारा लिंक करें';

  @override
  String get deviceStatus => 'डिवाइस स्थिति';

  @override
  String get desktopAndOtherDevices => 'डेस्कटॉप और अन्य डिवाइसेस';

  @override
  String get linkADeviceSoon => 'एक डिवाइस को लिंक करें (जल्द ही)';

  @override
  String get lastActiveFrom => 'पिछले सक्रिय से';

  @override
  String get tapADeviceToEditOrLogOut =>
      'संपादित करने या लॉगआउट करने के लिए एक डिवाइस पर टैप करें।';

  @override
  String get contactUs => 'हमसे संपर्क करें';

  @override
  String get supportChatSoon => 'समर्थन चैट (जल्द ही)';

  @override
  String get updateYourName => 'अपना नाम अपडेट करें';

  @override
  String get updateYourBio => 'अपना बायो अपडेट करें';

  @override
  String get edit => 'संपादित करें';

  @override
  String get about => 'के बारे में';

  @override
  String get oldPassword => 'पुराना पासवर्ड';

  @override
  String get deleteMyAccount => 'मेरा खाता हटाएं';

  @override
  String get passwordHasBeenChanged => 'पासवर्ड बदल दिया गया है';

  @override
  String get logoutFromAllDevices => 'सभी डिवाइसों से लॉगआउट करें?';

  @override
  String get updateYourPassword => 'अपना पासवर्ड अपडेट करें';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'अपना नाम दर्ज करें और ऐप्शनल प्रोफ़ाइल चित्र जोड़ें';

  @override
  String get privacyPolicy => 'गोपनीयता नीति';

  @override
  String get chat => 'चैट';

  @override
  String get send => 'भेजें';

  @override
  String get reportHasBeenSubmitted => 'आपकी रिपोर्ट प्रस्तुत की गई है';

  @override
  String get offline => 'ऑफ़लाइन';

  @override
  String get harassmentOrBullyingDescription =>
      'उपेक्षा या बुल्लिंग: इस विकल्प के तहत उपयोगकर्ता उन्हें या दूसरों को तंग करने वाले संदेशों, धमकियों या अन्य प्रकार के बुल्लिंग के संदेशों के खिलाफ रिपोर्ट कर सकते हैं।';

  @override
  String get spamOrScamDescription =>
      'स्पैम या धोखाधड़ी: इस विकल्प का उपयोग उन खातों के खिलाफ किया जा सकता है जो स्पैम संदेश, अनगढ़े विज्ञापन या दूसरों को धोखाधड़ी करने का प्रयास कर रहे हैं।';

  @override
  String get areYouSureToReportUserToAdmin =>
      'क्या आप इस उपयोगकर्ता के खिलाफ रिपोर्ट प्रस्तुत करने के लिए सुनिश्चित हैं?';

  @override
  String get groupWith => 'समूह के साथ';

  @override
  String get inappropriateContentDescription =>
      'अनुचित सामग्री: उपयोगकर्ता इस विकल्प को चुनकर किसी भी यौनता स्पष्ट सामग्री, घृणा भाषा या अन्य सामुदायिक मानकों का उल्लंघन करने वाली सामग्री की रिपोर्ट कर सकते हैं।';

  @override
  String get otherCategoryDescription =>
      'अन्य: यह एक ऐसा श्रेणी है जिसे उपर्युक्त श्रेणियों में आसानी से नहीं डाला जा सकता है। उपयोगकर्ताओं को अतिरिक्त विवरण प्रदान करने के लिए एक पाठ बॉक्स शामिल करने के लिए सहायक हो सकता है।';

  @override
  String get explainWhatHappens => 'यहां बताएं कि क्या होता है';

  @override
  String get loginAgain => 'फिर से लॉगिन करें!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'आपका सत्र समाप्त हो गया है, कृपया फिर से लॉगिन करें!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'आप इस उपयोगकर्ता को ब्लॉक करने के बारे में हैं। आप उसे चैट नहीं भेज सकते और उसे समूहों या प्रसारण में नहीं जोड़ सकते हैं!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'आप अपना खाता हटाने के बारे में हैं, आपका खाता फिर से उपयोगकर्ताओं की सूची में दिखाई नहीं देगा';

  @override
  String get admin => 'एडमिन';

  @override
  String get member => 'सदस्य';

  @override
  String get creator => 'निर्माता';

  @override
  String get currentDevice => 'वर्तमान डिवाइस';

  @override
  String get visits => 'यात्राएँ';

  @override
  String get chooseRoom => 'कमरा चुनें';

  @override
  String get deleteThisDeviceDesc =>
      'इस डिवाइस को हटाने से इसे तुरंत लॉगआउट कर दिया जाता है';

  @override
  String get youAreAboutToUpgradeToAdmin => 'आप प्रशासक बनाने के बारे में हैं';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'वर्तमान में लॉगिन अनुमति नहीं है। कृपया बाद में पुनः प्रयास करें।';

  @override
  String get dashboard => 'डैशबोर्ड';

  @override
  String get notification => 'सूचना';

  @override
  String get total => 'कुल';

  @override
  String get blocked => 'ब्लॉक किया गया';

  @override
  String get deleted => 'हटाया गया';

  @override
  String get accepted => 'स्वीकृत';

  @override
  String get notAccepted => 'स्वीकृत नहीं';

  @override
  String get web => 'वेब';

  @override
  String get android => 'एंड्रॉयड';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'विंडोज';

  @override
  String get other => 'अन्य';

  @override
  String get totalVisits => 'कुल दौरे';

  @override
  String get totalMessages => 'कुल संदेश';

  @override
  String get textMessages => 'टेक्स्ट संदेश';

  @override
  String get imageMessages => 'छवि संदेश';

  @override
  String get videoMessages => 'वीडियो संदेश';

  @override
  String get voiceMessages => 'आवाज संदेश';

  @override
  String get fileMessages => 'फ़ाइल संदेश';

  @override
  String get infoMessages => 'सूचना संदेश';

  @override
  String get voiceCallMessages => 'वॉयस कॉल संदेश';

  @override
  String get videoCallMessages => 'वीडियो कॉल संदेश';

  @override
  String get locationMessages => 'स्थान संदेश';

  @override
  String get directChat => 'स्थान संदेश';

  @override
  String get group => 'ग्रुप';

  @override
  String get broadcast => 'प्रसारण';

  @override
  String get messageCounter => 'संदेश गणना';

  @override
  String get roomCounter => 'कक्ष गणना';

  @override
  String get countries => 'देश';

  @override
  String get devices => 'डिवाइसेस';

  @override
  String get notificationTitle => 'सूचना शीर्षक';

  @override
  String get notificationDescription => 'सूचना विवरण';

  @override
  String get notificationsPage => 'सूचना पृष्ठ';

  @override
  String get updateFeedBackEmail => 'फीडबैक ईमेल अपडेट करें';

  @override
  String get setMaxMessageForwardAndShare =>
      'अधिकतम संदेश आगे भेजने और साझा करने की सेट करें';

  @override
  String get setNewPrivacyPolicyUrl => 'नई गोपनीयता नीति URL सेट करें';

  @override
  String get forgetPasswordExpireTime =>
      'पासवर्ड भूल जाने का समय समाप्त हो गया है';

  @override
  String get callTimeoutInSeconds => 'कॉल टाइमआउट (सेकंड में)';

  @override
  String get setMaxGroupMembers => 'अधिकतम समूह सदस्य सेट करें';

  @override
  String get setMaxBroadcastMembers => 'अधिकतम प्रसारण सदस्य सेट करें';

  @override
  String get allowCalls => 'कॉल्स अनुमति दें';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'इस विकल्प को सक्षम किया गया है, तो वीडियो और वॉयस कॉल की अनुमति होगी';

  @override
  String get allowAds => 'विज्ञापन अनुमति दें';

  @override
  String get allowMobileLogin => 'मोबाइल लॉगिन अनुमति दें';

  @override
  String get allowWebLogin => 'वेब लॉगिन अनुमति दें';

  @override
  String get messages => 'संदेश';

  @override
  String get appleStoreAppUrl => 'Apple स्टोर ऐप URL';

  @override
  String get googlePlayAppUrl => 'Google Play ऐप URL';

  @override
  String get privacyUrl => 'गोपनीयता URL';

  @override
  String get feedBackEmail => 'फीडबैक ईमेल';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'इस विकल्प को अक्षम किया गया है, तो चैट फ़ाइल्स, इमेज, वीडियो और स्थान भेजने की अनुमति नहीं होगी';

  @override
  String get allowSendMedia => 'मीडिया भेजने की अनुमति दें';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'इस विकल्प को अक्षम किया गया है, तो चैट प्रसारण ब्लॉक किया जाएगा';

  @override
  String get allowCreateBroadcast => 'प्रसारण बनाने की अनुमति दें';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'इस विकल्प को अक्षम किया गया है, तो चैट समूह ब्लॉक किया जाएगा';

  @override
  String get allowCreateGroups => 'समूह बनाने की अनुमति दें';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'इस विकल्प को अक्षम किया गया है, तो डेस्कटॉप लॉगिन या पंजीकरण (Windows और macOS) ब्लॉक किया जाएगा';

  @override
  String get allowDesktopLogin => 'डेस्कटॉप लॉगिन अनुमति दें';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'इस विकल्प को अक्षम किया गया है, तो वेब लॉगिन या पंजीकरण ब्लॉक किया जाएगा';

  @override
  String get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
      'इस विकल्प को सक्षम किया गया है, तो गूगल ऐड्स बैनर चैटों में प्रदर्शित होंगे';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => 'उपयोगकर्ता प्रोफ़ाइल';

  @override
  String get userInfo => 'उपयोगकर्ता जानकारी';

  @override
  String get fullName => 'पूरा नाम';

  @override
  String get bio => 'जीवनी';

  @override
  String get noBio => 'कोई जीवनी नहीं';

  @override
  String get verifiedAt => 'सत्यापित किया गया';

  @override
  String get country => 'देश';

  @override
  String get registerStatus => 'पंजीकरण स्थिति';

  @override
  String get registerMethod => 'पंजीकरण विधि';

  @override
  String get banTo => 'तक बैन किया गया है';

  @override
  String get deletedAt => 'हटाया गया है';

  @override
  String get createdAt => 'बनाया गया';

  @override
  String get updatedAt => 'अपडेट किया गया';

  @override
  String get reports => 'रिपोर्ट्स';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'उपयोगकर्ता डिवाइस विवरण देखने के लिए क्लिक करें';

  @override
  String get allDeletedMessages => 'सभी हटाएँ गई संदेश';

  @override
  String get voiceCallMessage => 'वॉयस कॉल संदेश';

  @override
  String get totalRooms => 'कुल कक्ष';

  @override
  String get directRooms => 'सीधे कक्ष';

  @override
  String get userAction => 'उपयोगकर्ता क्रिया';

  @override
  String get status => 'स्थिति';

  @override
  String get joinedAt => 'शामिल हुए';

  @override
  String get saveLogin => 'लॉगिन सहेजें';

  @override
  String get passwordIsRequired => 'पासवर्ड आवश्यक है';

  @override
  String get verified => 'सत्यापित';

  @override
  String get pending => 'अपूर्ण';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'विवरण आवश्यक है';

  @override
  String get seconds => 'सेकंड्स';

  @override
  String get clickToSeeAllUserInformations =>
      'उपयोगकर्ता जानकारी देखने के लिए क्लिक करें';

  @override
  String get clickToSeeAllUserCountries =>
      'उपयोगकर्ता देशों को देखने के लिए क्लिक करें';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'उपयोगकर्ता संदेश विवरण देखने के लिए क्लिक करें';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'उपयोगकर्ता कक्ष विवरण देखने के लिए क्लिक करें';

  @override
  String get clickToSeeAllUserReports =>
      'उपयोगकर्ता रिपोर्ट्स देखने के लिए क्लिक करें';

  @override
  String get banAt => 'बैन हुआ था';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'अब आप केवल पठनीय व्यवस्थापक के रूप में लॉगिन कर रहे हैं। आपके द्वारा किए गए सभी संपादन इसके लिए लागू नहीं होंगे क्योंकि यह एक परीक्षण संस्करण है।';

  @override
  String get createStory => 'कहानी बनाएं';

  @override
  String get writeACaption => 'कैप्शन लिखें...';

  @override
  String get storyCreatedSuccessfully => 'कहानी सफलतापूर्वक बनाई गई';

  @override
  String get stories => 'कहानियाँ';

  @override
  String get clear => 'साफ़ करें';

  @override
  String get clearCallsConfirm => 'कॉल साफ़ करने की पुष्टि करें';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'चुनें कि स्वचालित डाउनलोड कैसे काम करता है';

  @override
  String get whenUsingMobileData => 'मोबाइल डेटा का उपयोग करते समय';

  @override
  String get whenUsingWifi => 'वाई-फाई का उपयोग करते समय';

  @override
  String get image => 'चित्र';

  @override
  String get myPrivacy => 'मेरी गोपनीयता';

  @override
  String get createTextStory => 'टेक्स्ट कहानी बनाएं';

  @override
  String get createMediaStory => 'मीडिया कहानी बनाएं';

  @override
  String get camera => 'कैमरा';

  @override
  String get gallery => 'गैलरी';

  @override
  String get recentUpdate => 'हाल का अपडेट';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'नई कहानी जोड़ें';

  @override
  String get updateYourProfile => 'अपना प्रोफ़ाइल अपडेट करें';

  @override
  String get configureYourAccountPrivacy =>
      'अपने खाते की गोपनीयता कॉन्फ़िगर करें';

  @override
  String get youInPublicSearch => 'आप सार्वजनिक खोज में';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'आपका प्रोफ़ाइल सार्वजनिक खोज में दिखाई देता है और समूहों में जोड़ने के लिए उपलब्ध होता है';

  @override
  String get yourLastSeen => 'आपका अंतिम देखा गया';

  @override
  String get yourLastSeenInChats => 'चैट में आपका अंतिम देखा गया';

  @override
  String get startNewChatWithYou => 'आपके साथ नई चैट शुरू करें';

  @override
  String get yourStory => 'आपकी कहानी';

  @override
  String get forRequest => 'अनुरोध के लिए';

  @override
  String get public => 'सार्वजनिक';

  @override
  String get createYourStory => 'अपनी कहानी बनाएं';

  @override
  String get shareYourStatus => 'अपनी स्थिति साझा करें';

  @override
  String get oneSeenMessage => 'एक बार देखा गया संदेश';

  @override
  String get messageHasBeenViewed => 'संदेश देखा गया है';

  @override
  String get clickToSee => 'देखने के लिए क्लिक करें';

  @override
  String get images => 'चित्र';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get skipForNow => 'Skip for now';

  @override
  String get skipProfilePicture => 'Skip Profile Picture';

  @override
  String get skipProfilePictureMessage =>
      'Are you sure you want to skip adding a profile picture? You can add one later in settings.';
}
