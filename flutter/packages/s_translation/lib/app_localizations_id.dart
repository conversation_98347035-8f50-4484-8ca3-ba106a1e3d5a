// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Indonesian (`id`).
class AppLocalizationsId extends AppLocalizations {
  AppLocalizationsId([String locale = 'id']) : super(locale);

  @override
  String get done => 'Selesai';

  @override
  String get loading => 'Memuat...';

  @override
  String get messageHasBeenDeleted => 'Pesan telah dihapus';

  @override
  String get mute => 'Bisukan';

  @override
  String get cancel => 'Batal';

  @override
  String get typing => 'Mengetik...';

  @override
  String get ok => 'OK';

  @override
  String get recording => 'Merekam...';

  @override
  String get connecting => 'Menghubungkan...';

  @override
  String get deleteYouCopy => 'Hapus salinan Anda';

  @override
  String get unMute => 'Batalkan bisu';

  @override
  String get delete => 'Hapus';

  @override
  String get report => 'Laporkan';

  @override
  String get leaveGroup => 'Tinggalkan grup';

  @override
  String get areYouSureToPermitYourCopyThisActionCantUndo =>
      'Apakah Anda yakin ingin mengizinkan salinan Anda? Tindakan ini tidak dapat dibatalkan';

  @override
  String get areYouSureToLeaveThisGroupThisActionCantUndo =>
      'Apakah Anda yakin ingin meninggalkan grup ini? Tindakan ini tidak dapat dibatalkan';

  @override
  String get leaveGroupAndDeleteYourMessageCopy =>
      'Tinggalkan grup dan hapus salinan pesan Anda';

  @override
  String get vMessageInfoTrans => 'Info Pesan';

  @override
  String get updateTitleTo => 'Perbarui judul menjadi';

  @override
  String get updateImage => 'Perbarui gambar';

  @override
  String get joinedBy => 'Bergabung oleh';

  @override
  String get promotedToAdminBy => 'Dipromosikan menjadi admin oleh';

  @override
  String get dismissedToMemberBy => 'Dikeluarkan menjadi anggota oleh';

  @override
  String get leftTheGroup => 'Meninggalkan grup';

  @override
  String get you => 'Anda';

  @override
  String get kickedBy => 'Dikeluarkan oleh';

  @override
  String get groupCreatedBy => 'Grup dibuat oleh';

  @override
  String get addedYouToNewBroadcast => 'Menambahkan Anda ke siaran baru';

  @override
  String get download => 'Unduh';

  @override
  String get copy => 'Salin';

  @override
  String get info => 'Info';

  @override
  String get share => 'Bagikan';

  @override
  String get forward => 'Teruskan';

  @override
  String get reply => 'Balas';

  @override
  String get reacted => 'Reacted';

  @override
  String get replied => 'Replied';

  @override
  String get deleteFromAll => 'Hapus dari semua';

  @override
  String get deleteFromMe => 'Hapus dari saya';

  @override
  String get downloading => 'Mengunduh...';

  @override
  String get fileHasBeenSavedTo => 'Berkas telah disimpan ke';

  @override
  String get online => 'Online';

  @override
  String get youDontHaveAccess => 'Anda tidak memiliki akses';

  @override
  String get replyToYourSelf => 'Balas ke diri Anda sendiri';

  @override
  String get repliedToYourSelf => 'Telah dibalas ke diri Anda sendiri';

  @override
  String get audioCall => 'Panggilan Audio';

  @override
  String get ring => 'Berdering';

  @override
  String get canceled => 'Dibatalkan';

  @override
  String get timeout => 'Waktu habis';

  @override
  String get rejected => 'Ditolak';

  @override
  String get finished => 'Selesai';

  @override
  String get inCall => 'Dalam panggilan';

  @override
  String get sessionEnd => 'Sesi berakhir';

  @override
  String get yesterday => 'Kemarin';

  @override
  String get today => 'Hari ini';

  @override
  String get textFieldHint => 'Ketik pesan...';

  @override
  String get files => 'Berkas';

  @override
  String get location => 'Lokasi';

  @override
  String get shareMediaAndLocation => 'Bagikan media dan lokasi';

  @override
  String get thereIsVideoSizeBiggerThanAllowedSize =>
      'Ada ukuran video yang lebih besar dari yang diizinkan';

  @override
  String get thereIsFileHasSizeBiggerThanAllowedSize =>
      'Ada berkas dengan ukuran yang lebih besar dari yang diizinkan';

  @override
  String get makeCall => 'Buat panggilan';

  @override
  String get areYouWantToMakeVideoCall =>
      'Apakah Anda ingin membuat panggilan video?';

  @override
  String get areYouWantToMakeVoiceCall =>
      'Apakah Anda ingin membuat panggilan suara?';

  @override
  String get vMessagesInfoTrans => 'Info Pesan';

  @override
  String get star => 'Bintang';

  @override
  String get minutes => 'Menit';

  @override
  String get sendMessage => 'Kirim pesan';

  @override
  String get deleteUser => 'Hapus pengguna';

  @override
  String get actions => 'Tindakan';

  @override
  String get youAreAboutToDeleteThisUserFromYourList =>
      'Anda akan menghapus pengguna ini dari daftar Anda';

  @override
  String get updateBroadcastTitle => 'Perbarui judul siaran';

  @override
  String get usersAddedSuccessfully => 'Pengguna berhasil ditambahkan';

  @override
  String get broadcastSettings => 'Pengaturan Siaran';

  @override
  String get addParticipants => 'Tambahkan Peserta';

  @override
  String get broadcastParticipants => 'Peserta Siaran';

  @override
  String get updateGroupDescription => 'Perbarui deskripsi grup';

  @override
  String get updateGroupTitle => 'Perbarui judul grup';

  @override
  String get groupSettings => 'Pengaturan Grup';

  @override
  String get description => 'Deskripsi';

  @override
  String get muteNotifications => 'Bisukan pemberitahuan';

  @override
  String get groupParticipants => 'Peserta Grup';

  @override
  String get blockUser => 'Blokir pengguna';

  @override
  String get areYouSureToBlock => 'Apakah Anda yakin ingin memblokir';

  @override
  String get userPage => 'Halaman pengguna';

  @override
  String get starMessage => 'Bintang pesan';

  @override
  String get showMedia => 'Tampilkan media';

  @override
  String get reportUser => 'Laporkan pengguna';

  @override
  String get groupName => 'Nama grup';

  @override
  String get changeSubject => 'Ubah subjek';

  @override
  String get titleIsRequired => 'Judul diperlukan';

  @override
  String get createBroadcast => 'Buat Siaran';

  @override
  String get broadcastName => 'Nama siaran';

  @override
  String get createGroup => 'Buat Grup';

  @override
  String get forgetPassword => 'Lupa Kata Sandi';

  @override
  String get globalSearch => 'Pencarian Global';

  @override
  String get dismissesToMember => 'Mengeluarkan menjadi anggota';

  @override
  String get setToAdmin => 'Atur sebagai admin';

  @override
  String get kickMember => 'Keluarkan anggota';

  @override
  String get youAreAboutToDismissesToMember =>
      'Anda akan mengeluarkan menjadi anggota';

  @override
  String get youAreAboutToKick => 'Anda akan mengeluarkan';

  @override
  String get groupMembers => 'Anggota Grup';

  @override
  String get tapForPhoto => 'Ketuk untuk foto';

  @override
  String get weHighRecommendToDownloadThisUpdate =>
      'Kami sangat merekomendasikan untuk mengunduh pembaruan ini';

  @override
  String get newGroup => 'Grup Baru';

  @override
  String get newBroadcast => 'Siaran Baru';

  @override
  String get starredMessage => 'Pesan yang Dibintangi';

  @override
  String get settings => 'Pengaturan';

  @override
  String get chats => 'OBROLAN';

  @override
  String get recentUpdates => 'Pembaruan Terbaru';

  @override
  String get startChat => 'Mulai obrolan';

  @override
  String get newUpdateIsAvailable => 'Pembaruan baru tersedia';

  @override
  String get emailNotValid => 'Email tidak valid';

  @override
  String get passwordMustHaveValue => 'Kata sandi harus memiliki nilai';

  @override
  String get error => 'Kesalahan';

  @override
  String get password => 'Kata sandi';

  @override
  String get login => 'Masuk';

  @override
  String get needNewAccount => 'Butuh akun baru?';

  @override
  String get register => 'Daftar';

  @override
  String get nameMustHaveValue => 'Nama harus memiliki nilai';

  @override
  String get passwordNotMatch => 'Kata sandi tidak cocok';

  @override
  String get name => 'Nama';

  @override
  String get email => 'Email';

  @override
  String get confirmPassword => 'Konfirmasi kata sandi';

  @override
  String get alreadyHaveAnAccount => 'Sudah memiliki akun?';

  @override
  String get logOut => 'Keluar';

  @override
  String get back => 'Kembali';

  @override
  String get sendCodeToMyEmail => 'Kirim kode ke email saya';

  @override
  String get invalidLoginData => 'Data masuk tidak valid';

  @override
  String get userEmailNotFound => 'Email pengguna tidak ditemukan';

  @override
  String get yourAccountBlocked => 'Akun Anda telah diblokir';

  @override
  String get yourAccountDeleted => 'Akun Anda telah dihapus';

  @override
  String get userAlreadyRegister => 'Pengguna sudah terdaftar';

  @override
  String get codeHasBeenExpired => 'Kode telah kedaluwarsa';

  @override
  String get invalidCode => 'Kode tidak valid';

  @override
  String get whileAuthCanFindYou =>
      'Saat otentikasi tidak dapat menemukan Anda';

  @override
  String get userRegisterStatusNotAcceptedYet =>
      'Status pendaftaran pengguna belum diterima';

  @override
  String get deviceHasBeenLogoutFromAllDevices =>
      'Perangkat telah keluar dari semua perangkat';

  @override
  String get userDeviceSessionEndDeviceDeleted =>
      'Sesi perangkat pengguna berakhir perangkat dihapus';

  @override
  String get noCodeHasBeenSendToYouToVerifyYourEmail =>
      'Tidak ada kode yang dikirimkan kepada Anda untuk memverifikasi email Anda';

  @override
  String get roomAlreadyInCall => 'Ruang sudah dalam panggilan';

  @override
  String get peerUserInCallNow => 'Pengguna dalam panggilan sekarang';

  @override
  String get callNotAllowed => 'Panggilan tidak diizinkan';

  @override
  String get peerUserDeviceOffline => 'Perangkat pengguna lainnya offline';

  @override
  String get emailMustBeValid => 'Email harus valid';

  @override
  String get wait2MinutesToSendMail => 'Tunggu 2 menit untuk mengirim email';

  @override
  String get codeMustEqualToSixNumbers => 'Kode harus sama dengan enam angka';

  @override
  String get newPasswordMustHaveValue => 'Kata sandi baru harus memiliki nilai';

  @override
  String get confirmPasswordMustHaveValue =>
      'Konfirmasi kata sandi harus memiliki nilai';

  @override
  String get congregationsYourAccountHasBeenAccepted =>
      'Selamat, akun Anda telah diterima';

  @override
  String get yourAccountIsUnderReview =>
      'Akun Anda sedang dalam proses peninjauan';

  @override
  String get waitingList => 'Daftar Tunggu';

  @override
  String get welcome => 'Selamat datang';

  @override
  String get retry => 'Coba lagi';

  @override
  String get deleteMember => 'Hapus anggota';

  @override
  String get profile => 'Profil';

  @override
  String get broadcastInfo => 'Info Siaran';

  @override
  String get updateTitle => 'Perbarui judul';

  @override
  String get members => 'Anggota';

  @override
  String get addMembers => 'Tambahkan Anggota';

  @override
  String get success => 'Berhasil';

  @override
  String get media => 'Media';

  @override
  String get docs => 'Dokumen';

  @override
  String get links => 'Tautan';

  @override
  String get soon => 'Segera';

  @override
  String get unStar => 'Batal bintang';

  @override
  String get updateGroupDescriptionWillUpdateAllGroupMembers =>
      'Perbarui deskripsi grup akan memperbarui semua anggota grup';

  @override
  String get updateNickname => 'Perbarui nama panggilan';

  @override
  String get groupInfo => 'Info Grup';

  @override
  String get youNotParticipantInThisGroup =>
      'Anda bukan peserta dalam grup ini';

  @override
  String get search => 'Cari';

  @override
  String get mediaLinksAndDocs => 'Media, Tautan, dan Dokumen';

  @override
  String get starredMessages => 'Pesan yang Dibintangi';

  @override
  String get nickname => 'Nama panggilan';

  @override
  String get none => 'Tidak ada';

  @override
  String get yes => 'Ya';

  @override
  String get no => 'Tidak';

  @override
  String get exitGroup => 'Keluar dari Grup';

  @override
  String get clickToAddGroupDescription =>
      'Klik untuk menambahkan deskripsi grup';

  @override
  String get unBlockUser => 'Buka blokir pengguna';

  @override
  String get areYouSureToUnBlock => 'Apakah Anda yakin ingin membuka blokir';

  @override
  String get contactInfo => 'Info Kontak';

  @override
  String get audio => 'Audio';

  @override
  String get video => 'Video';

  @override
  String get hiIamUse => 'Halo, saya menggunakan';

  @override
  String get on => 'Hidup';

  @override
  String get off => 'Mati';

  @override
  String get unBlock => 'Buka Blokir';

  @override
  String get block => 'Blokir';

  @override
  String get chooseAtLestOneMember => 'Pilih setidaknya satu anggota';

  @override
  String get close => 'Tutup';

  @override
  String get next => 'Selanjutnya';

  @override
  String get appMembers => 'Anggota Aplikasi';

  @override
  String get create => 'Buat';

  @override
  String get upgradeToAdmin => 'Promosikan ke admin';

  @override
  String get update => 'Perbarui';

  @override
  String get deleteChat => 'Hapus obrolan';

  @override
  String get clearChat => 'Bersihkan obrolan';

  @override
  String get showHistory => 'Tampilkan riwayat';

  @override
  String get groupIcon => 'Ikon Grup';

  @override
  String get tapToSelectAnIcon => 'Ketuk untuk memilih ikon';

  @override
  String get groupDescription => 'Deskripsi Grup';

  @override
  String get more => 'Lebih banyak';

  @override
  String get messageInfo => 'Info Pesan';

  @override
  String get successfullyDownloadedIn => 'Berhasil diunduh di';

  @override
  String get delivered => 'Dikirim';

  @override
  String get read => 'Dibaca';

  @override
  String get orLoginWith => 'atau masuk dengan';

  @override
  String get resetPassword => 'Atur ulang kata sandi';

  @override
  String get otpCode => 'Kode OTP';

  @override
  String get newPassword => 'Kata sandi baru';

  @override
  String get areYouSure => 'Apakah Anda yakin?';

  @override
  String get broadcastMembers => 'Anggota Siaran';

  @override
  String get phone => 'Telepon';

  @override
  String get users => 'Pengguna';

  @override
  String get calls => 'Panggilan';

  @override
  String get yourAreAboutToLogoutFromThisAccount =>
      'Anda akan keluar dari akun ini';

  @override
  String get noUpdatesAvailableNow =>
      'Tidak ada pembaruan yang tersedia saat ini';

  @override
  String get dataPrivacy => 'Privasi Data';

  @override
  String get allDataHasBeenBackupYouDontNeedToManageSaveTheDataByYourself =>
      'Semua data telah dicadangkan, Anda tidak perlu mengelola penyimpanan data sendiri! Jika Anda keluar dan masuk kembali, Anda akan melihat semua obrolan sama dengan versi web';

  @override
  String get account => 'Akun';

  @override
  String get linkedDevices => 'Perangkat Tersambung';

  @override
  String get storageAndData => 'Penyimpanan dan Data';

  @override
  String get tellAFriend => 'Beritahu teman';

  @override
  String get help => 'Bantuan';

  @override
  String get blockedUsers => 'Pengguna yang Diblokir';

  @override
  String get inAppAlerts => 'Pemberitahuan dalam Aplikasi';

  @override
  String get language => 'Bahasa';

  @override
  String get adminNotification => 'Notifikasi Admin';

  @override
  String get checkForUpdates => 'Periksa Pembaruan';

  @override
  String get linkByQrCode => 'Tautkan dengan Kode QR';

  @override
  String get deviceStatus => 'Status Perangkat';

  @override
  String get desktopAndOtherDevices => 'Desktop, dan perangkat lainnya';

  @override
  String get linkADeviceSoon => 'Tautkan Perangkat (Segera)';

  @override
  String get lastActiveFrom => 'Terakhir aktif dari';

  @override
  String get tapADeviceToEditOrLogOut =>
      'Ketuk perangkat untuk mengedit atau keluar.';

  @override
  String get contactUs => 'Hubungi Kami';

  @override
  String get supportChatSoon => 'Chat Dukungan (Segera)';

  @override
  String get updateYourName => 'Perbarui nama Anda';

  @override
  String get updateYourBio => 'Perbarui biodata Anda';

  @override
  String get edit => 'Edit';

  @override
  String get about => 'Tentang';

  @override
  String get oldPassword => 'Kata sandi lama';

  @override
  String get deleteMyAccount => 'Hapus akun saya';

  @override
  String get passwordHasBeenChanged => 'Kata sandi telah diubah';

  @override
  String get logoutFromAllDevices => 'Keluar dari semua perangkat?';

  @override
  String get updateYourPassword => 'Perbarui kata sandi Anda';

  @override
  String get enterNameAndAddOptionalProfilePicture =>
      'Masukkan nama Anda dan tambahkan foto profil opsional';

  @override
  String get privacyPolicy => 'Kebijakan Privasi';

  @override
  String get chat => 'Obrolan';

  @override
  String get send => 'Kirim';

  @override
  String get reportHasBeenSubmitted => 'Laporan Anda telah diajukan';

  @override
  String get offline => 'Offline';

  @override
  String get harassmentOrBullyingDescription =>
      'Pelecehan atau Penggertakan: Pilihan ini memungkinkan pengguna melaporkan individu yang menargetkan mereka atau orang lain dengan pesan pelecehan, ancaman, atau bentuk pelecehan lainnya.';

  @override
  String get spamOrScamDescription =>
      'Spam atau Penipuan: Pilihan ini untuk melaporkan akun yang mengirimkan pesan spam, iklan yang tidak diminta, atau berusaha menipu orang lain.';

  @override
  String get areYouSureToReportUserToAdmin =>
      'Apakah Anda yakin ingin melaporkan pengguna ini ke admin?';

  @override
  String get groupWith => 'Grup dengan';

  @override
  String get inappropriateContentDescription =>
      'Konten yang Tidak Pantas: Pengguna dapat memilih opsi ini untuk melaporkan materi yang mengandung konten seksual eksplisit, ujaran kebencian, atau konten lain yang melanggar standar komunitas.';

  @override
  String get otherCategoryDescription =>
      'Lainnya: Kategori ini dapat digunakan untuk pelanggaran yang tidak mudah dimasukkan ke dalam kategori di atas. Mungkin berguna untuk menyertakan kotak teks agar pengguna dapat memberikan detail tambahan.';

  @override
  String get explainWhatHappens => 'Jelaskan apa yang terjadi';

  @override
  String get loginAgain => 'Masuk lagi!';

  @override
  String get yourSessionIsEndedPleaseLoginAgain =>
      'Sesi Anda telah berakhir, silakan masuk kembali!';

  @override
  String get aboutToBlockUserWithConsequences =>
      'Anda akan memblokir pengguna ini. Anda tidak dapat mengirimkan pesan kepadanya dan tidak dapat menambahkannya ke grup atau siaran!';

  @override
  String get youAreAboutToDeleteYourAccountYourAccountWillNotAppearAgainInUsersList =>
      'Anda akan menghapus akun Anda. Akun Anda tidak akan muncul lagi dalam daftar pengguna';

  @override
  String get admin => 'Admin';

  @override
  String get member => 'Anggota';

  @override
  String get creator => 'Pembuat';

  @override
  String get currentDevice => 'Perangkat Saat Ini';

  @override
  String get visits => 'Kunjungan';

  @override
  String get chooseRoom => 'Pilih ruangan';

  @override
  String get deleteThisDeviceDesc =>
      'Menghapus perangkat ini berarti langsung keluar dari perangkat ini';

  @override
  String get youAreAboutToUpgradeToAdmin =>
      'Anda akan mengupgrade menjadi admin';

  @override
  String get microphonePermissionMustBeAccepted =>
      'Microphone permission must be accepted';

  @override
  String get microphoneAndCameraPermissionMustBeAccepted =>
      'Microphone and camera permission must be accepted';

  @override
  String get loginNowAllowedNowPleaseTryAgainLater =>
      'Saat ini login tidak diizinkan. Silakan coba lagi nanti.';

  @override
  String get dashboard => 'Dasbor';

  @override
  String get notification => 'Pemberitahuan';

  @override
  String get total => 'Total';

  @override
  String get blocked => 'Diblokir';

  @override
  String get deleted => 'Dihapus';

  @override
  String get accepted => 'Diterima';

  @override
  String get notAccepted => 'Tidak Diterima';

  @override
  String get web => 'Web';

  @override
  String get android => 'Android';

  @override
  String get macOs => 'macOS';

  @override
  String get windows => 'Windows';

  @override
  String get other => 'Lainnya';

  @override
  String get totalVisits => 'Total Kunjungan';

  @override
  String get totalMessages => 'Total Pesan';

  @override
  String get textMessages => 'Pesan Teks';

  @override
  String get imageMessages => 'Pesan Gambar';

  @override
  String get videoMessages => 'Pesan Video';

  @override
  String get voiceMessages => 'Pesan Suara';

  @override
  String get fileMessages => 'Pesan Berkas';

  @override
  String get infoMessages => 'Pesan Informasi';

  @override
  String get voiceCallMessages => 'Pesan Panggilan Suara';

  @override
  String get videoCallMessages => 'Pesan Panggilan Video';

  @override
  String get locationMessages => 'Pesan Lokasi';

  @override
  String get directChat => 'Obrolan Langsung';

  @override
  String get group => 'Grup';

  @override
  String get broadcast => 'Siaran';

  @override
  String get messageCounter => 'Penghitung Pesan';

  @override
  String get roomCounter => 'Penghitung Ruang';

  @override
  String get countries => 'Negara';

  @override
  String get devices => 'Perangkat';

  @override
  String get notificationTitle => 'Judul Pemberitahuan';

  @override
  String get notificationDescription => 'Deskripsi Pemberitahuan';

  @override
  String get notificationsPage => 'Halaman Pemberitahuan';

  @override
  String get updateFeedBackEmail => 'Perbarui Email Masukan';

  @override
  String get setMaxMessageForwardAndShare =>
      'Atur Maksimum Pesan yang Dapat Diteruskan dan Dibagikan';

  @override
  String get setNewPrivacyPolicyUrl => 'Atur URL Kebijakan Privasi Baru';

  @override
  String get forgetPasswordExpireTime => 'Waktu Kadaluarsa Lupa Kata Sandi';

  @override
  String get callTimeoutInSeconds => 'Batas Waktu Panggilan (dalam detik)';

  @override
  String get setMaxGroupMembers => 'Atur Maksimum Anggota Grup';

  @override
  String get setMaxBroadcastMembers => 'Atur Maksimum Anggota Siaran';

  @override
  String get allowCalls => 'Izinkan Panggilan';

  @override
  String get ifThisOptionEnabledTheVideoAndVoiceCallWillBeAllowed =>
      'Jika opsi ini diaktifkan, panggilan video dan suara akan diizinkan';

  @override
  String get allowAds => 'Izinkan Iklan';

  @override
  String get allowMobileLogin => 'Izinkan Login di Ponsel';

  @override
  String get allowWebLogin => 'Izinkan Login di Web';

  @override
  String get messages => 'Pesan';

  @override
  String get appleStoreAppUrl => 'URL Aplikasi Apple Store';

  @override
  String get googlePlayAppUrl => 'URL Aplikasi Google Play';

  @override
  String get privacyUrl => 'URL Privasi';

  @override
  String get feedBackEmail => 'Email Masukan';

  @override
  String get ifThisOptionDisabledTheSendChatFilesImageVideosAndLocationWillBeBlocked =>
      'Jika opsi ini dinonaktifkan, pengiriman berkas chat, gambar, video, dan lokasi akan diblokir';

  @override
  String get allowSendMedia => 'Izinkan Pengiriman Media';

  @override
  String get ifThisOptionDisabledTheCreateChatBroadcastWillBeBlocked =>
      'Jika opsi ini dinonaktifkan, pembuatan siaran chat akan diblokir';

  @override
  String get allowCreateBroadcast => 'Izinkan Pembuatan Siaran';

  @override
  String get ifThisOptionDisabledTheCreateChatGroupsWillBeBlocked =>
      'Jika opsi ini dinonaktifkan, pembuatan grup chat akan diblokir';

  @override
  String get allowCreateGroups => 'Izinkan Pembuatan Grup';

  @override
  String get ifThisOptionDisabledTheDesktopLoginOrRegisterWindowsMacWillBeBlocked =>
      'Jika opsi ini dinonaktifkan, login atau registrasi desktop (Windows dan macOS) akan diblokir';

  @override
  String get allowDesktopLogin => 'Izinkan Login Desktop';

  @override
  String get ifThisOptionDisabledTheWebLoginOrRegisterWillBeBlocked =>
      'Jika opsi ini dinonaktifkan, login atau registrasi web akan diblokir';

  @override
  String get ifThisOptionDisabledTheMobileLoginOrRegisterWillBeBlockedOnAndroidIosOnly =>
      'Jika opsi ini diaktifkan, iklan banner Google akan muncul dalam obrolan';

  @override
  String get ifThisOptionEnabledTheGoogleAdsBannerWillAppearInChats =>
      'If this option is enabled, the Google Ads banner will appear in chats.';

  @override
  String get userProfile => 'Profil Pengguna';

  @override
  String get userInfo => 'Informasi Pengguna';

  @override
  String get fullName => 'Nama Lengkap';

  @override
  String get bio => 'Biodata';

  @override
  String get noBio => 'Tidak ada biodata';

  @override
  String get verifiedAt => 'Terverifikasi pada';

  @override
  String get country => 'Negara';

  @override
  String get registerStatus => 'Status Registrasi';

  @override
  String get registerMethod => 'Metode Registrasi';

  @override
  String get banTo => 'Diblokir hingga';

  @override
  String get deletedAt => 'Dihapus pada';

  @override
  String get createdAt => 'Dibuat pada';

  @override
  String get updatedAt => 'Diperbarui pada';

  @override
  String get reports => 'Laporan';

  @override
  String get clickToSeeAllUserDevicesDetails =>
      'Klik untuk melihat semua detail perangkat pengguna';

  @override
  String get allDeletedMessages => 'Semua Pesan yang Dihapus';

  @override
  String get voiceCallMessage => 'Pesan Panggilan Suara';

  @override
  String get totalRooms => 'Total Ruangan';

  @override
  String get directRooms => 'Ruangan Langsung';

  @override
  String get userAction => 'Tindakan Pengguna';

  @override
  String get status => 'Status';

  @override
  String get joinedAt => 'Bergabung pada';

  @override
  String get saveLogin => 'Simpan Login';

  @override
  String get passwordIsRequired => 'Kata Sandi Diperlukan';

  @override
  String get verified => 'Terverifikasi';

  @override
  String get pending => 'Tertunda';

  @override
  String get ios => 'iOS';

  @override
  String get descriptionIsRequired => 'Deskripsi Diperlukan';

  @override
  String get seconds => 'detik';

  @override
  String get clickToSeeAllUserInformations =>
      'Klik untuk melihat semua informasi pengguna';

  @override
  String get clickToSeeAllUserCountries =>
      'Klik untuk melihat semua negara pengguna';

  @override
  String get clickToSeeAllUserMessagesDetails =>
      'Klik untuk melihat semua detail pesan pengguna';

  @override
  String get clickToSeeAllUserRoomsDetails =>
      'Klik untuk melihat semua detail ruangan pengguna';

  @override
  String get clickToSeeAllUserReports =>
      'Klik untuk melihat semua laporan pengguna';

  @override
  String get banAt => 'Diblokir pada';

  @override
  String get nowYouLoginAsReadOnlyAdminAllEditYouDoneWillNotAppliedDueToThisIsTestVersion =>
      'Sekarang Anda masuk sebagai admin hanya baca. Semua perubahan yang Anda lakukan tidak akan diterapkan karena ini adalah versi uji coba.';

  @override
  String get createStory => 'Buat Cerita';

  @override
  String get writeACaption => 'Tulis keterangan...';

  @override
  String get storyCreatedSuccessfully => 'Cerita Berhasil Dibuat';

  @override
  String get stories => 'Cerita';

  @override
  String get clear => 'Hapus';

  @override
  String get clearCallsConfirm => 'Konfirmasi hapus panggilan';

  @override
  String get chooseHowAutomaticDownloadWorks =>
      'Pilih cara kerja unduhan otomatis';

  @override
  String get whenUsingMobileData => 'Saat menggunakan data seluler';

  @override
  String get whenUsingWifi => 'Saat menggunakan Wi-Fi';

  @override
  String get image => 'Gambar';

  @override
  String get myPrivacy => 'Privasi Saya';

  @override
  String get createTextStory => 'Buat Cerita Teks';

  @override
  String get createMediaStory => 'Buat Cerita Media';

  @override
  String get camera => 'Kamera';

  @override
  String get gallery => 'Galeri';

  @override
  String get recentUpdate => 'Pembaruan terbaru';

  @override
  String get viewedUpdates => 'Viewed updates';

  @override
  String get addNewStory => 'Tambahkan cerita baru';

  @override
  String get updateYourProfile => 'Perbarui profil Anda';

  @override
  String get configureYourAccountPrivacy => 'Konfigurasikan privasi akun Anda';

  @override
  String get youInPublicSearch => 'Anda dalam pencarian publik';

  @override
  String get yourProfileAppearsInPublicSearchAndAddingForGroups =>
      'Profil Anda muncul di pencarian publik dan penambahan untuk grup';

  @override
  String get yourLastSeen => 'Terakhir dilihat';

  @override
  String get yourLastSeenInChats => 'Terakhir dilihat di chat';

  @override
  String get startNewChatWithYou => 'Mulai obrolan baru dengan Anda';

  @override
  String get yourStory => 'Cerita Anda';

  @override
  String get forRequest => 'Untuk permintaan';

  @override
  String get public => 'Publik';

  @override
  String get createYourStory => 'Buat cerita Anda';

  @override
  String get shareYourStatus => 'Bagikan status Anda';

  @override
  String get oneSeenMessage => 'Satu pesan dilihat';

  @override
  String get messageHasBeenViewed => 'Pesan telah dilihat';

  @override
  String get clickToSee => 'Klik untuk melihat';

  @override
  String get images => 'Gambar';

  @override
  String get switchAccount => 'Switch Account';

  @override
  String get addAccount => 'Add Account';

  @override
  String get manageYourAccounts => 'Manage your accounts';

  @override
  String get addAnotherAccount => 'Add another account';

  @override
  String get selectAccountToSwitchTo => 'Select account to switch to';

  @override
  String get errorLoadingAccounts => 'Error loading accounts';

  @override
  String get noAccountsFound => 'No accounts found';

  @override
  String get active => 'Active';

  @override
  String get removeAccount => 'Remove Account';

  @override
  String get areYouSureRemoveAccount =>
      'Are you sure you want to remove this account?';

  @override
  String get accountRemoved => 'Account removed';

  @override
  String get errorRemovingAccount => 'Error removing account';

  @override
  String switchedToAccount(Object name) {
    return 'Switched to $name';
  }

  @override
  String get errorSwitchingAccount => 'Error switching account';

  @override
  String get accountAddedSuccessfully => 'Account added successfully';

  @override
  String get addProfilePicture => 'Add Profile Picture';

  @override
  String get addProfilePictureSubtitle =>
      'Add a profile picture to help others recognize you';

  @override
  String get pleaseSelectProfilePicture => 'Please select a profile picture';

  @override
  String get uploading => 'Uploading...';

  @override
  String get continueText => 'Continue';

  @override
  String get skipForNow => 'Skip for now';

  @override
  String get skipProfilePicture => 'Skip Profile Picture';

  @override
  String get skipProfilePictureMessage =>
      'Are you sure you want to skip adding a profile picture? You can add one later in settings.';
}
