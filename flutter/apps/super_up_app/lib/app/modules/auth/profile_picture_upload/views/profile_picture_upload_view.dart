// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../../../core/api_service/profile/profile_api_service.dart';
import '../../../../core/widgets/wide_constraints.dart';
import '../controllers/profile_picture_upload_controller.dart';

class ProfilePictureUploadView extends StatefulWidget {
  const ProfilePictureUploadView({super.key});

  @override
  State<ProfilePictureUploadView> createState() =>
      _ProfilePictureUploadViewState();
}

class _ProfilePictureUploadViewState extends State<ProfilePictureUploadView> {
  late ProfilePictureUploadController controller;

  @override
  void initState() {
    super.initState();
    controller = ProfilePictureUploadController(
      ProfileApiService.init(),
    );
  }

  @override
  void dispose() {
    controller.onClose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          controller.handleBackPress(context);
        }
      },
      child: ResponsiveBuilder(
        builder: (context, sizingInformation) => WideConstraints(
          enable: sizingInformation.isDesktop,
          child: CupertinoPageScaffold(
          navigationBar: CupertinoNavigationBar(
            leading: CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () => controller.handleBackPress(context),
              child: const Icon(
                CupertinoIcons.back,
                color: CupertinoColors.systemBlue,
              ),
            ),
            middle: Text(S.of(context).addProfilePicture),
            backgroundColor: CupertinoColors.systemBackground.withOpacity(0.9),
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(height: context.height * .03),

                  // Subtitle
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Text(
                      S.of(context).addProfilePictureSubtitle,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  SizedBox(height: context.height * .05),

                  // Profile Picture Picker
                  ValueListenableBuilder<ProfilePictureUploadState>(
                    valueListenable: controller,
                    builder: (context, state, child) {
                      return VImagePicker(
                        onDone: (VPlatformFile file) {
                          controller.value =
                              controller.value.copyWith(selectedImage: file);
                        },
                        initImage: state.selectedImage ??
                            VPlatformFile.fromAssets(
                              assetsPath: "assets/ic_addphoto.png",
                            ),
                        withCrop: true,
                        size: 150,
                      );
                    },
                  ),

                  SizedBox(height: context.height * .05),

                  // Upload Button
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: ValueListenableBuilder<ProfilePictureUploadState>(
                      valueListenable: controller,
                      builder: (context, state, child) {
                        return SElevatedButton(
                          title: state.isUploading
                              ? S.of(context).uploading
                              : S.of(context).continueText,
                          onPress: state.isUploading
                              ? () {} // Disabled state
                              : () {
                                  controller.uploadProfilePicture(context);
                                },
                        );
                      },
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Skip Button
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: ValueListenableBuilder<ProfilePictureUploadState>(
                      valueListenable: controller,
                      builder: (context, state, child) {
                        return CupertinoButton(
                          onPressed: state.isUploading
                              ? null
                              : () => controller.skipProfilePicture(context),
                          child: Text(
                            S.of(context).skipForNow,
                            style: TextStyle(
                              color: state.isUploading
                                  ? Colors.grey.shade400
                                  : Colors.grey,
                              fontSize: 16,
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  const SizedBox(height: 50),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
