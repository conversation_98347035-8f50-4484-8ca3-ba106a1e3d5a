// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';
import 'package:s_translation/generated/l10n.dart';

import '../../../../core/api_service/profile/profile_api_service.dart';
import '../../../home/<USER>/views/home_view.dart';

class ProfilePictureUploadState {
  VPlatformFile? selectedImage;
  bool isUploading;

  ProfilePictureUploadState({
    this.selectedImage,
    this.isUploading = false,
  });

  ProfilePictureUploadState copyWith({
    VPlatformFile? selectedImage,
    bool? isUploading,
  }) {
    return ProfilePictureUploadState(
      selectedImage: selectedImage ?? this.selectedImage,
      isUploading: isUploading ?? this.isUploading,
    );
  }
}

class ProfilePictureUploadController
    extends ValueNotifier<ProfilePictureUploadState>
    implements SBaseController {
  final ProfileApiService profileService;

  ProfilePictureUploadController(this.profileService)
      : super(ProfilePictureUploadState());

  void selectImage() async {
    final image = await VAppPick.getCroppedImage();
    if (image != null) {
      value = value.copyWith(selectedImage: image);
    }
  }

  void uploadProfilePicture(BuildContext context) async {
    if (value.selectedImage == null) {
      VAppAlert.showErrorSnackBar(
        message: S.of(context).pleaseSelectProfilePicture,
        context: context,
      );
      return;
    }

    await vSafeApiCall<String>(
      onLoading: () async {
        value = value.copyWith(isUploading: true);
        VAppAlert.showLoading(context: context);
      },
      onError: (exception, trace) {
        value = value.copyWith(isUploading: false);
        Navigator.of(context).pop();
        VAppAlert.showOkAlertDialog(
          context: context,
          title: S.of(context).error,
          content: exception.toString(),
        );
      },
      request: () async {
        return await profileService.updateImage(value.selectedImage!);
      },
      onSuccess: (response) async {
        value = value.copyWith(isUploading: false);

        // Update the stored profile with new image
        final newProfile = AppAuth.myProfile.copyWith(
          baseUser: AppAuth.myProfile.baseUser.copyWith(userImage: response),
        );
        await VAppPref.setMap(SStorageKeys.myProfile.name, newProfile.toMap());
        AppAuth.setProfileNull();

        Navigator.of(context).pop(); // Close loading dialog

        // Navigate to home screen
        context.toPage(
          const HomeView(),
          withAnimation: true,
          removeAll: true,
        );
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  void skipProfilePicture(BuildContext context) async {
    // Show confirmation dialog
    final result = await VAppAlert.showAskYesNoDialog(
      context: context,
      title: S.of(context).skipProfilePicture,
      content: S.of(context).skipProfilePictureMessage,
    );

    if (result == 1) {
      context.toPage(
        const HomeView(),
        withAnimation: true,
        removeAll: true,
      );
    }
  }

  @override
  void onClose() {
    dispose();
  }

  @override
  void onInit() {
    // Initialize if needed
  }
}
